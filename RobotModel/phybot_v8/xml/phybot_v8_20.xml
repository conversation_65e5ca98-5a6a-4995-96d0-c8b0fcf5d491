<mujoco model="phybot_v6">
  <compiler angle="radian" meshdir="./meshes/"/>
  <option timestep="0.01" iterations='20' solver='PGS' gravity='0 0 -9.81' apirate='1000'>
  </option>
  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
    <rgba haze="0.15 0.25 0.35 1"/>
    <global azimuth="150" elevation="-20"/>
  </visual>

  <asset>
    <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0" width="512" height="3072"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3"
      markrgb="0.8 0.8 0.8" width="300" height="300"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2"/>
  </asset>

  <worldbody>
    <light pos="1 0 5" dir="0 0 -1" directional="true"/>
    <geom name="floor" pos="0 0 0" size="0 0 0.05" type="plane" material="groundplane" friction="1.0 1 1"/>
    <geom pos="2 0.0 0.0" type="box" size="1 1 0.3" quat="1.0 0.0 0.0 0.0" />
    <geom pos="-2 0.0 0.0" type="box" size="1 1 0.3" quat="1.0 0.0 0.0 0.0" />
    <geom pos="0 -2 0.0" type="box" size="1 1 0.3" quat="1.0 0.0 0.0 0.0" />
    <geom pos="0 2 0.0" type="box" size="1 1 0.3" quat="1.0 0.0 0.0 0.0" />

  </worldbody>

  <default>
    <default class="phybot">
      <joint damping="1" armature="0.1"/>
      <default class="visual">
        <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.75294 0.75294 0.75294 1"/>
      </default>
      <default class="collision">
        <geom group="1" mass="0" density="0"  friction="1.0 0.8 0.7"/>

        <default class="foot">
          <geom type="sphere" size="0.001"/>
          <default class="foot1">
            <geom size="0.001" contype="0" conaffinity="0" density="0"/>
          </default>
          <default class="foot2">
            <geom size="0.001"/>
          </default>
        </default>
      </default>
      <site size="0.001" rgba="0.5 0.5 0.5 0.3" group="4"/>
    </default>
    <default class="phybot_motor">
      <motor gear="1" ctrllimited="true" ctrlrange="-300  300" />
    </default>

  </default>

  <asset>
    <mesh name="base_link" file="base_link.STL"/>
    <mesh name="left_hip_pitch" file="left_hip_pitch.STL"/>
    <mesh name="left_hip_roll" file="left_hip_roll.STL"/>
    <mesh name="left_hip_yaw" file="left_hip_yaw.STL"/>
    <mesh name="left_knee" file="left_knee.STL"/>
    <mesh name="left_ankle_pitch" file="left_ankle_pitch.STL"/>
    <mesh name="left_ankle_roll" file="left_ankle_roll.STL"/>
    <mesh name="left_toe" file="left_toe.STL"/>
    <mesh name="right_hip_pitch" file="right_hip_pitch.STL"/>
    <mesh name="right_hip_roll" file="right_hip_roll.STL"/>
    <mesh name="right_hip_yaw" file="right_hip_yaw.STL"/>
    <mesh name="right_knee" file="right_knee.STL"/>
    <mesh name="right_ankle_pitch" file="right_ankle_pitch.STL"/>
    <mesh name="right_ankle_roll" file="right_ankle_roll.STL"/>
    <mesh name="right_toe" file="right_toe.STL"/>
    <mesh name="waist_yaw" file="waist_yaw.STL"/>
    <mesh name="waist_roll" file="waist_roll.STL"/>
    <mesh name="torso" file="torso.STL"/>
    <mesh name="neck_yaw" file="neck_yaw.STL"/>
    <mesh name="neck_pitch" file="neck_pitch.STL"/>
    <mesh name="left_shoulder_pitch" file="left_shoulder_pitch.STL"/>
    <mesh name="left_shoulder_roll" file="left_shoulder_roll.STL"/>
    <mesh name="left_shoulder_yaw" file="left_shoulder_yaw.STL"/>
    <mesh name="left_elbow_pitch" file="left_elbow_pitch.STL"/>
    <mesh name="left_elbow_yaw" file="left_elbow_yaw.STL"/>
    <mesh name="left_wrist" file="left_wrist.STL"/>
    <mesh name="right_shoulder_pitch" file="right_shoulder_pitch.STL"/>
    <mesh name="right_shoulder_roll" file="right_shoulder_roll.STL"/>
    <mesh name="right_shoulder_yaw" file="right_shoulder_yaw.STL"/>
    <mesh name="right_elbow_pitch" file="right_elbow_pitch.STL"/>
    <mesh name="right_elbow_yaw" file="right_elbow_yaw.STL"/>
    <mesh name="right_wrist" file="right_wrist.STL"/>
  </asset>

  <worldbody>
    <body name="base_link" pos="0 0 1" childclass="phybot">
      <inertial pos="-5.4954e-07 0.0014987 -0.043143" quat="0.499994 0.500051 0.500006 0.499949" mass="1.3664" diaginertia="0.0100136 0.0096706 0.00922994"/>
      <joint name="floating_base_joint" type="free" limited="false" actuatorfrclimited="false"/>
      <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.49804 0.49804 0.49804 1" mesh="base_link"/>
      <geom type="mesh" rgba="0.49804 0.49804 0.49804 1" mesh="base_link"/>
      <body name="left_hip_pitch" pos="0 0.049982 -0.028857" quat="0.965926 -0.25882 0 0">
        <inertial pos="-5.84174e-09 0.0470439 -1.26304e-07" quat="0.612276 0.612269 -0.353725 0.353728" mass="1.35964" diaginertia="0.0043037 0.00428305 0.00343704"/>
        <joint name="left_hip_pitch" pos="0 0 0" axis="0 1 0" range="-2.62 2.62" actuatorfrcrange="-300 300"/>
        <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_hip_pitch"/>
        <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="left_hip_pitch"/>
        <body name="left_hip_roll" pos="-0.0075 0.108 0" quat="0.965926 0.25882 0 0">
          <inertial pos="0.00890485 -5.28612e-08 -0.0281584" quat="0.999925 0.0122601 -2.40697e-08 1.96311e-06" mass="1.23462" diaginertia="0.00332614 0.00315544 0.00162796"/>
          <joint name="left_hip_roll" pos="0 0 0" axis="1 0 0" range="-0.17 1.92" actuatorfrcrange="-150 150"/>
          <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_hip_roll"/>
          <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="left_hip_roll"/>
          <body name="left_hip_yaw" pos="0.008 0 -0.115">
            <inertial pos="-0.00684956 -0.000648552 -0.110182" quat="0.999187 -0.0379826 0.00938699 -0.00967289" mass="5.92707" diaginertia="0.0568569 0.053761 0.0202642"/>
            <joint name="left_hip_yaw" pos="0 0 0" axis="0 0 1" range="-1.57 1.57" actuatorfrcrange="-150 150"/>
            <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_hip_yaw"/>
            <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="left_hip_yaw"/>
            <body name="left_knee" pos="-0.0205 0 -0.31">
              <inertial pos="0.00888132 -2.52669e-05 -0.189166" quat="0.999931 0.0109268 0.000898678 -0.00428901" mass="2.86581" diaginertia="0.0371268 0.036586 0.00361215"/>
              <joint name="left_knee" pos="0 0 0" axis="0 1 0" range="0 2.62" actuatorfrcrange="-300 300"/>
              <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_knee"/>
              <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="left_knee"/>
              <body name="left_ankle_pitch" pos="0.01 0 -0.405">
                <inertial pos="0 0 0" mass="0.0183186" diaginertia="2.81e-06 2.01e-06 2.01e-06"/>
                <joint name="left_ankle_pitch" pos="0 0 0" axis="0 1 0" range="-0.87 0.52" actuatorfrcrange="-100 100"/>
                <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_ankle_pitch"/>
                <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="left_ankle_pitch"/>
                <body name="left_ankle_roll" pos="0.01 0 -0.04">
                  <inertial pos="0.0681557 0.000432398 -0.028151" quat="0.393525 0.638277 -0.373569 0.546065" mass="0.734236" diaginertia="0.00387363 0.0023668 0.00209721"/>
                  <joint name="left_ankle_roll" pos="0 0 0" axis="1 0 0" range="-0.35 0.35" actuatorfrcrange="-30 30"/>
                  <!-- <geom class="collision" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_ankle_roll"/> -->
                  <geom class="collision" type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="left_ankle_roll"/>
                  <geom class="collision" pos="0.12318 -0.0035126 -0.034074" quat="1 0 0 0" type="mesh"  rgba="0.498039 0.498039 0.498039 1" mesh="left_toe"/>
                </body>
              </body>
            </body>
          </body>
        </body>
      </body>
      <body name="right_hip_pitch" pos="0 -0.049982 -0.028857" quat="0.965926 0.25882 0 0">
        <inertial pos="-4.56163e-08 -0.0470439 -1.05883e-07" quat="0.35347 0.35347 -0.612421 0.612421" mass="1.35964" diaginertia="0.00430371 0.00428308 0.00343704"/>
        <joint name="right_hip_pitch" pos="0 0 0" axis="0 1 0" range="-2.62 2.62" actuatorfrcrange="-300 300"/>
        <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_hip_pitch"/>
        <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_hip_pitch"/>
        <body name="right_hip_roll" pos="-0.0075 -0.108 0" quat="0.965926 -0.25882 0 0">
          <inertial pos="0.00890486 -2.2818e-08 -0.0281585" quat="0.999925 0.0122601 0 0" mass="1.23463" diaginertia="0.00332614 0.00315544 0.00162796"/>
          <joint name="right_hip_roll" pos="0 0 0" axis="1 0 0" range="-1.92 0.17" actuatorfrcrange="-150 150"/>
          <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_hip_roll"/>
          <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_hip_roll"/>
          <body name="right_hip_yaw" pos="0.008 0 -0.115">
            <inertial pos="-0.00685186 0.000647666 -0.11018" quat="0.999188 -0.0379855 -0.00939173 0.00964376" mass="5.92742" diaginertia="0.05686 0.0537647 0.0202638"/>
            <joint name="right_hip_yaw" pos="0 0 0" axis="0 0 1" range="-1.57 1.57" actuatorfrcrange="-150 150"/>
            <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_hip_yaw"/>
            <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_hip_yaw"/>
            <body name="right_knee" pos="-0.0205 0 -0.31">
              <inertial pos="0.00887957 2.46027e-05 -0.189168" quat="0.704031 0.00835493 0.00708117 0.710085" mass="2.8658" diaginertia="0.0371259 0.0365851 0.00361218"/>
              <joint name="right_knee" pos="0 0 0" axis="0 1 0" range="0 2.62" actuatorfrcrange="-300 300"/>
              <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_knee"/>
              <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_knee"/>
              <body name="right_ankle_pitch" pos="0.01 0 -0.405">
                <inertial pos="0 0 0" quat="0.707107 0 0 0.707107" mass="0.0183186" diaginertia="2.80504e-06 2.01314e-06 2.01314e-06"/>
                <joint name="right_ankle_pitch" pos="0 0 0" axis="0 1 0" range="-0.87 0.52" actuatorfrcrange="-100 100"/>
                <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_ankle_pitch"/>
                <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_ankle_pitch"/>
                <body name="right_ankle_roll" pos="0.01 0 -0.04">
                  <inertial pos="0.0681552 -0.00043261 -0.0281509" quat="0.0288308 0.7364 0.0451988 0.674419" mass="0.734234" diaginertia="0.0038709 0.00375899 0.000664927"/>
                  <joint name="right_ankle_roll" pos="0 0 0" axis="1 0 0" range="-0.35 0.35" actuatorfrcrange="-30 30"/>
                  <!-- <geom class="collision" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_ankle_roll"/> -->
                  <geom class="collision" type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_ankle_roll"/>
                  <!-- <geom class="collision" pos="0.12318 0.0035126 -0.034074" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_toe"/> -->
                  <geom class="collision" pos="0.12318 0.0035126 -0.034074" quat="1 0 0 0" type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="right_toe"/>
                </body>
              </body>
            </body>
          </body>
        </body>
      </body>
      <body name="waist_yaw" pos="0 0.0015 0.018312">
        <inertial pos="-0.00409284 -4.61918e-08 0.0410593" quat="0.978871 7.39983e-07 -0.20448 2.73573e-06" mass="0.663024" diaginertia="0.00144423 0.00129998 0.000982718"/>
        <joint name="waist_yaw" pos="0 0 0" axis="0 0 1" range="-1.57 1.57" actuatorfrcrange="-150 150"/>
        <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="waist_yaw"/>
        <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="waist_yaw"/>
        <body name="waist_roll" pos="-0.0025 0 0.11">
          <inertial pos="0.00172819 -0.00017164 0.25698" quat="0.999996 0.0014576 0.00226783 0.000671448" mass="20.0979" diaginertia="0.511352 0.497869 0.153423"/>
          <joint name="waist_roll" pos="0 0 0" axis="1 0 0" range="-0.79 0.79" actuatorfrcrange="-150 150"/>
          <geom type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="waist_roll"/>
          <geom type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="waist_roll"/>
          <geom pos="0.0025 -0.0015 0.22676" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="torso"/>
          <geom pos="0.0025 -0.0015 0.22676" type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="torso"/>
          <geom pos="-0.014781 0 0.310578" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="neck_yaw"/>
          <geom pos="-0.014781 0 0.310578" quat="1 0 0 0" type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="neck_yaw"/>
          <geom pos="-0.014781 0 0.443078" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="neck_pitch"/>
          <geom pos="-0.014781 0 0.443078" quat="1 0 0 0" type="mesh" rgba="0.498039 0.498039 0.498039 1" mesh="neck_pitch"/>
          <body name="left_shoulder_pitch" pos="-0.014781 0.12147 0.280573">
            <inertial pos="-0.00409862 0.0384065 -0.00292121" quat="0.703344 0.685785 0.088101 0.165062" mass="0.430914" diaginertia="0.000806836 0.000698656 0.000509909"/>
            <joint name="left_shoulder_pitch" pos="0 0 0" axis="0 1 0" range="-2.79 0.87" actuatorfrcrange="-150 150"/>
            <geom class="visual" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_shoulder_pitch"/>
            <body name="left_shoulder_roll" pos="0 0.098527 -0.0074956">
              <inertial pos="-7.31513e-05 0.00103304 -0.101936" quat="0.709041 0.00280731 0.00530091 0.705142" mass="2.16794" diaginertia="0.0162446 0.0157814 0.00301039"/>
              <joint name="left_shoulder_roll" pos="0 0 0" axis="1 0 0" range="-0.17 3.14" actuatorfrcrange="-150 150"/>
              <geom class="visual" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_shoulder_roll"/>
              <geom class="visual" pos="0 0 -0.1125" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_shoulder_yaw"/>
              <body name="left_elbow_pitch" pos="0 0 -0.25">
                <inertial pos="-0.000621133 0.000785304 -0.147195" quat="0.71467 0.00735623 -0.00625954 0.699395" mass="1.37725" diaginertia="0.0070759 0.00707254 0.00441264"/>
                <joint name="left_elbow_pitch" pos="0 0 0" axis="0 1 0" range="-2.36 0" actuatorfrcrange="-36 36"/>
                <geom class="visual" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_elbow_pitch"/>
                <geom class="visual" pos="0 0 -0.0875" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_elbow_yaw"/>
                <geom class="visual" pos="0 0.001333 -0.28403" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="left_wrist"/>
              </body>
            </body>
          </body>
          <body name="right_shoulder_pitch" pos="-0.014781 -0.12447 0.280573">
            <inertial pos="-0.00409868 -0.0384063 -0.00292251" quat="0.703368 0.685757 -0.0880812 -0.165084" mass="0.430914" diaginertia="0.000806835 0.000698652 0.000509908"/>
            <joint name="right_shoulder_pitch" pos="0 0 0" axis="0 1 0" range="-2.79 0.87" actuatorfrcrange="-150 150"/>
            <geom class="visual" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_shoulder_pitch"/>
            <body name="right_shoulder_roll" pos="0 -0.098527 -0.0074956">
              <inertial pos="-7.34743e-05 -0.00103289 -0.101936" quat="0.706862 -0.0058497 0.00999234 0.707256" mass="2.16798" diaginertia="0.0162511 0.0157801 0.00300512"/>
              <joint name="right_shoulder_roll" pos="0 0 0" axis="1 0 0" range="-3.14 0.17" actuatorfrcrange="-150 150"/>
              <geom class="visual" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_shoulder_roll"/>
              <geom class="visual" pos="0 0 -0.1125" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_shoulder_yaw"/>
              <body name="right_elbow_pitch" pos="0 0 -0.25">
                <inertial pos="-0.000618389 -0.000784258 -0.147203" quat="0.708355 -0.00309297 -0.001495 0.705848" mass="1.37719" diaginertia="0.00939184 0.00936459 0.00105825"/>
                <joint name="right_elbow_pitch" pos="0 0 0" axis="0 1 0" range="-2.36 0" actuatorfrcrange="-36 36"/>
                <geom class="visual" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_elbow_pitch"/>
                <geom class="visual" pos="0 0 -0.0875" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_elbow_yaw"/>
                <geom class="visual" pos="0 -0.001333 -0.28403" quat="1 0 0 0" type="mesh" contype="0" conaffinity="0" group="1" density="0" rgba="0.498039 0.498039 0.498039 1" mesh="right_wrist"/>
              </body>
            </body>
          </body>
        </body>
      </body>


        <body name="imu_link" pos="-0.059 0 -0.068">
        <inertial pos="0 0 0" mass="0" diaginertia="0 0 0" />
        <geom class="visual" type="sphere" rgba="0.792157 0.819608 0.933333 1" size="0.02" />
        <site name="site_imu" pos="0.01 0 0" size='0.01 0.01 0.01' group="4" euler="0 0 0"  />
      </body>  


    </body>
  </worldbody>

  <actuator>
    <motor class="phybot_motor" name="left_hip_pitch" joint="left_hip_pitch"/>  
    <motor class="phybot_motor" name="left_hip_roll" joint="left_hip_roll"/>
    <motor class="phybot_motor" name="left_hip_yaw" joint="left_hip_yaw"/>
    <motor class="phybot_motor" name="left_knee" joint="left_knee"/>
    <motor class="phybot_motor" name="left_ankle_pitch" joint="left_ankle_pitch"/>
    <motor class="phybot_motor" name="left_ankle_roll" joint="left_ankle_roll"/>

    <motor class="phybot_motor" name="right_hip_pitch" joint="right_hip_pitch"/>  
    <motor class="phybot_motor" name="right_hip_roll" joint="right_hip_roll"/>
    <motor class="phybot_motor" name="right_hip_yaw" joint="right_hip_yaw"/>
    <motor class="phybot_motor" name="right_knee" joint="right_knee"/>
    <motor class="phybot_motor" name="right_ankle_pitch" joint="right_ankle_pitch"/>
    <motor class="phybot_motor" name="right_ankle_roll" joint="right_ankle_roll"/>


    <motor class="phybot_motor" name="waist_yaw" joint="waist_yaw"/>
    <motor class="phybot_motor" name="waist_roll" joint="waist_roll"/>

    <motor class="phybot_motor" name="left_shoulder_pitch" joint="left_shoulder_pitch"/>
    <motor class="phybot_motor" name="left_shoulder_roll" joint="left_shoulder_roll"/>
    <motor class="phybot_motor" name="left_elbow_pitch" joint="left_elbow_pitch"/>

    <motor class="phybot_motor" name="right_shoulder_pitch" joint="right_shoulder_pitch"/>
    <motor class="phybot_motor" name="right_shoulder_roll" joint="right_shoulder_roll"/>
    <motor class="phybot_motor" name="right_elbow_pitch" joint="right_elbow_pitch"/>


  </actuator>


    <sensor>
      <!-- <force name="R_forcesensor" site="R_site_forcesensor" cutoff="2000.0" />
      <force name="L_forcesensor" site="L_site_forcesensor" cutoff="2000.0" />
      <force name="R_forcesensor_toe" site="R_site_forcesensor_toe" cutoff="2000.0" />
      <force name="L_forcesensor_toe" site="L_site_forcesensor_toe" cutoff="2000.0" />
      <accelerometer name="imu_acc"             site="site_imu" noise='0.005' cutoff='157'/>
      <framequat     name='orientation'         objtype='site'  noise='0.001' objname='site_imu'/>
      <framepos      name='position'            objtype='site'  noise='0.001' objname='site_imu'/>
      <gyro          name='angular-velocity'    site='site_imu' noise='0.005' cutoff='34.9'/>
      <velocimeter   name='linear-velocity'     site='site_imu' noise='0.001' cutoff='30'/>
      <magnetometer  name='magnetometer'        site='site_imu'/> -->

      <framequat name="baselink-quat" objtype="site" objname="site_imu" />
      <velocimeter name="baselink-velocity" site="site_imu" />
      <gyro name="baselink-gyro" site="site_imu" />
      <accelerometer name="baselink-baseAcc" site="site_imu" />
  </sensor>



    <keyframe>
    <key name="home"
      qpos=" 0 0 3.0 1 0 0 0  -0.2 0 0 0.4 -0.2 0   -0.2 0 0 0.4 -0.2 0  0 0   0.2 0 -0.2 -0.2 0 -0.2"/>
  </keyframe>


</mujoco>
