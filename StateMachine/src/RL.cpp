#include "RL.h"
#include <iostream>

RL::RL() : rl(nullptr) {
    setCurrentState(State::RL);
    printCurrentState();
}

void RL::start(Event event, DataPackage& data) {
    if (rl == nullptr) {

        rl = std::make_unique<rl_deploy>();
    }
}

void RL::run(Event event, DataPackage& data) {


    rl->GetDataFromPackage(data);
    rl->Step();
    rl->SetDataToPackage(data);
}

void RL::exit(Event event, DataPackage& data) {
    // 可添加清理操作
}
bool RL::CanExit(State& current, State& next, DataPackage& data)
 {
    return true;
}
