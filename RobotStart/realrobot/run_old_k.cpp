#include <chrono>
#include <cstdint>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <iostream>
#include <memory>
#include <mutex>
#include <new>
#include <string>


#include "array_safety.h"
#include "DataPackage/include/DataPackage.h"
// #include "GLFW_callbacks.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/basic_file_sink.h> // 用于文件输出

#include "StateMachine/include/statemachinemanager.h"
#include "Joystick/include/joystick_int.h"
#include"../MotorList/include/MotorList.hpp"
#include"../MotorList/include/realtime_controller.hpp"
#include <fstream>  // 追加: 用于写入文件

#include "HipnucReader.h"

// #define DATALOG


int main()
{

    DataPackage package;
    package.init();
  

  
    Joystick joystick;

    joystick.init();

    HipnucReader reader;
    reader.start();
  
    // DataLogger Logger;
    // spdlog::info("DataLogger");
  
    StateMachineManager manager;
    spdlog::info("StateMachineManager start");
    manager.init(package);

    // spdlog::info("manager.init");
    // 设置日志级别
    spdlog::set_level(spdlog::level::info); // 只记录INFO级别及以上的日志

    
  
    spdlog::info("model load success!");


    std::string config_yaml = "../MotorList/config/Motorlist_mini_stable_change_pd.yaml";

    std::vector<std::string> motor_name_list;
    Eigen::VectorXd P_control_vector, D_control_vector, direction_vector;

    loadMotorConfig(config_yaml, motor_name_list, P_control_vector, D_control_vector, direction_vector);
    int motorNum = motor_name_list.size();
    std::cout<<"ccccccc: "<<motorNum<<std::endl;
    MotorList motorlist;

#ifdef DATALOG
    std::ofstream foutData;
    foutData.open("../logdata/datacollection.txt", std::ios::out);
    Eigen::VectorXd dataL = Eigen::VectorXd::Zero(74 + 6 * motorNum);
#endif

    motorlist.Init(config_yaml, motorNum, motor_name_list, package);
    std::cout << "Motorlist init complete! " << std::endl;

    Eigen::VectorXd pos_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd vel_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd tor_actual = Eigen::VectorXd::Zero(motorNum);

    Eigen::VectorXd pos_desire = Eigen::VectorXd::Zero(motorNum);

    Eigen::VectorXd zero = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd vel_desire = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd tor_desire = Eigen::VectorXd::Zero(motorNum);

    // 保存数据的容器（使用 vector 存储每次循环的数据）
    std::vector<std::vector<double>> pos_actual_data;
    std::vector<std::vector<double>> vel_actual_data;
    std::vector<std::vector<double>> pos_desire_data;
    std::vector<std::vector<double>> vel_desire_data;

    std::vector<std::vector<double>> tor_actual_data;
    std::vector<std::vector<double>> tor_desire_data;
    
    // 保存表头
    std::vector<std::string> header = {"Time"};
    for (const auto& motor_name : motor_name_list) {
        header.push_back(motor_name);
    }

    auto record_start_time = std::chrono::steady_clock::now();
    auto log_start_time = std::chrono::steady_clock::now();

    period_info P_info;
    periodic_task_init(&P_info, package.control_period);
//-------------------------------------------------------------------------------------

    while(1){

          // ************ test ****************

          auto run_start_time = std::chrono::steady_clock::now();

          joystick.GetDataFromPackage(package);
          joystick.run();
          joystick.SetDataToPackage(package);

          motorlist.GetStates(pos_actual, vel_actual, tor_actual, direction_vector, package, 1);

          //waiting imu data

          package.getIMUdata(reader);





          manager.GetDataFromPackage(package);
          manager.run(package);
          manager.SetDataToPackage(package); 


         std::cout<<"pos_desire: "<<pos_desire(11)<<std::endl;

          motorlist.SetCommands(zero, vel_desire, tor_desire, 0 * P_control_vector, 0 *D_control_vector, direction_vector, package);
  


          wait_rest_of_period(&P_info);

          auto record_now_time = std::chrono::steady_clock::now();
         
          std::chrono::duration<double> elapsed_record = record_now_time - run_start_time;
          std::cout<<"elapsed_record"<<elapsed_record.count()<<std::endl;

          auto log_now_time = std::chrono::steady_clock::now();
         
          std::chrono::duration<double> elapsed_log = log_now_time - log_start_time;


#ifdef DATALOG
          dataL[0] = elapsed_log.count();
          dataL.block(1, 0, 6, 1) = package.generalized_q_actual.head(6);
          dataL.block(7, 0, 6, 1) = package.generalized_q_dot_actual.head(6);
          dataL.block(13, 0, 6, 1) = package.generalized_q_desired.head(6);
          dataL.block(19, 0, 6, 1) = package.generalized_q_dot_desired.head(6);
          dataL.block(25 , 0, motorNum, 1) = package.generalized_q_desired.tail(motorNum);
          //49
          dataL.block(25 + 1 * motorNum, 0, motorNum, 1) = package.generalized_q_dot_desired.tail(motorNum);
          //73
          dataL.block(25 + 2 * motorNum, 0, motorNum, 1) = package.motor_Torque_desire;  //only for real robot
          //97
          dataL.block(25 + 3 * motorNum, 0, motorNum, 1) = package.motor_pos;
          //121
          dataL.block(25 + 4 * motorNum, 0, motorNum, 1) = package.motor_vel;
          //145
          dataL.block(25 + 5 * motorNum, 0, motorNum, 1) = package.motor_torque;
          //169
          dataL.block(25 + 6 * motorNum, 0, 3, 1) = package.imu_lin_acc;
          //172
          dataL.block(25 + 6 * motorNum+3, 0, 3, 1) = package.vel_base_obser_left;
          //175
          dataL[31 + 6 * motorNum] = package.contact_force[2];
          dataL[32 + 6 * motorNum] = package.contact_force[8];
          dataL[33 + 6 * motorNum] = package.FootNum;
          dataL[34 + 6 * motorNum] = package.contact_flag[0];
          dataL[35 + 6 * motorNum] = package.contact_flag[1];
          //180
          dataL.block(36 + 6 * motorNum, 0, 3, 1) = package.feet_l_Pos_W_actual;
          dataL.block(39 + 6 * motorNum, 0, 3, 1) = package.feet_r_Pos_W_actual;
          //186
          dataL.block(42 + 6 * motorNum, 0, 3, 1) = package.feet_l_Pos_W_desire;
          dataL.block(45 + 6 * motorNum, 0, 3, 1) = package.feet_r_Pos_W_desire;
          
          dataL.block(48 + 6 * motorNum, 0, 3, 1) = package.feet_l_LinVel_W_actual;
          dataL.block(51 + 6 * motorNum, 0, 3, 1) = package.feet_r_LinVel_W_actual;
          dataL.block(54 + 6 * motorNum, 0, 3, 1) = package.feet_l_LinVel_W_desire;
          dataL.block(57 + 6 * motorNum, 0, 3, 1) = package.feet_r_LinVel_W_desire;
          dataL.block(60 + 6 * motorNum, 0, 3, 1) = package.feet_l_EulerZYX_W_actual;
          dataL.block(63 + 6 * motorNum, 0, 3, 1) = package.feet_r_EulerZYX_W_actual;
          dataL[66 + 6 * motorNum] = package.feet_l_EulerZ_W_desire;
          dataL[67 + 6 * motorNum] = package.feet_r_EulerZ_W_desire;
          dataL[68 + 6 * motorNum] = package.feet_l_OmegaZ_W_desire;
          dataL[69 + 6 * motorNum] = package.feet_r_OmegaZ_W_desire;
          dataL.block(70 + 6 * motorNum, 0, 2, 1) = package.u_B;

          
          // Write to file
          dataLog(dataL, foutData);
#endif
        // auto log_start_time = std::chrono::steady_clock::now();

        //   Logger.SaveDataToFile(package, elapsed_record.count(), state_estimator);

        //   // 将当前时间和数据添加到内存中的数据容器
        //   std::vector<double> pos_actual_row = {elapsed_record.count()};
        //   for (int i = 0; i < pos_actual.size(); i++) {
        //       pos_actual_row.push_back(pos_actual[i]);
        //   }
        //   pos_actual_data.push_back(pos_actual_row);
  
        //   std::vector<double> vel_actual_row = {elapsed_record.count()};
        //   for (int i = 0; i < vel_actual.size(); i++) {
        //       vel_actual_row.push_back(vel_actual[i]);
        //   }
        //   vel_actual_data.push_back(vel_actual_row);
  
        //   std::vector<double> pos_desire_row = {elapsed_record.count()};
        //   for (int i = 0; i < pos_desire.size(); i++) {
        //       pos_desire_row.push_back(pos_desire[i]);
        //   }
        //   pos_desire_data.push_back(pos_desire_row);
  
        //   std::vector<double> vel_desire_row = {elapsed_record.count()};
        //   for (int i = 0; i < vel_desire.size(); i++) {
        //       vel_desire_row.push_back(vel_desire[i]);
        //   }
        //   vel_desire_data.push_back(vel_desire_row);

        // std::vector<double> tor_actual_row = {elapsed_record.count()};
        //   for (int i = 0; i < tor_actual.size(); i++) {
        //       tor_actual_row.push_back(tor_actual[i]);
        //   }
        //   tor_actual_data.push_back(tor_actual_row);

        // std::vector<double> tor_desire_row = {elapsed_record.count()};
        //   for (int i = 0; i < tor_desire.size(); i++) {
        //       tor_desire_row.push_back(tor_desire[i]);
        //   }
        //   tor_desire_data.push_back(tor_desire_row);
  
          // 如果需要退出循环（比如设置一个最大时间或者某种条件）
        //   if (elapsed_log.count() >= 60) break;  

        // auto log_now_time = std::chrono::steady_clock::now();

        // std::chrono::duration<double> elapsed_log = log_now_time - log_start_time;
        // std::cout<<"elapsed_log"<<elapsed_log.count()<<std::endl;

    }

 

#ifdef DATALOG
      foutData.close();
#endif

    return 0;
}


