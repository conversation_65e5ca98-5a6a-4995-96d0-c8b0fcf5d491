#include"../MotorList/include/MotorList.hpp"
#include"../MotorList/include/realtime_controller.hpp"
#include "ZeroState/include/ZeroState.h"

#include <fstream>  // 追加: 用于写入文件

#include "HipnucReader.h"


int main()
{

    std::string config_yaml = "../MotorList/config/Motorlist_mini_stable_change_pd.yaml";
    double total_time = 15.0;
    int mode =1;
    double current_time;
    std::vector<std::string> motor_name_list;
    Eigen::VectorXd P_control_vector, D_control_vector, direction_vector;

    loadMotorConfig(config_yaml, motor_name_list, P_control_vector, D_control_vector, direction_vector);
    int motorNum = motor_name_list.size();


    HipnucReader reader;
    reader.start();

    // std::cout << "Loaded motor count: " << motorNum << std::endl;
    // for (int i = 0; i < motorNum; ++i) {
    //     std::cout << motor_name_list[i] << " -> P_control: " << P_control_vector(i) 
    //     << " -> D_control: " << D_control_vector(i) 
    //     << " -> direction: " << dir_control_vector(i) 
    //     << std::endl;
    // }

    Eigen::VectorXd pos_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd vel_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd tor_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd init_pos_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd init_vel_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd pos_desire = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd vel_desire = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd tor_desire = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd zero_vector = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd ones_vector = Eigen::VectorXd::Ones(motorNum) *0.1;


    ZeroState gotozero;

    MotorList motorlist;


    motorlist.Init(config_yaml, motorNum, motor_name_list);
    std::cout << "Motorlist init complete! " << std::endl;


    period_info P_info;
    periodic_task_init(&P_info, 0.002);
    while(1){

        auto run_start_time = std::chrono::steady_clock::now();

        // std::cout<<"xx: "<<reader.ang_vel_new<<std::endl;make

        motorlist.GetStates(pos_actual, vel_actual, tor_actual, direction_vector);

        std::cout<<"pos_actual: "<< pos_actual(11)<<std::endl;

        if(mode == 1)
        {

            if(current_time< total_time)
            {
                ThirdpolyVector(pos_actual, zero_vector, gotozero.zero_pose, zero_vector,
                        total_time, current_time, pos_desire, vel_desire);
            }

            else {
                current_time = 0;
                mode = 2;

            }

        }

        else if(mode == 2)
        {

            if(current_time < total_time)
            {
                ThirdpolyVector(pos_actual, zero_vector, zero_vector, zero_vector,
                        total_time, current_time, pos_desire, vel_desire);
            }

            else {
                current_time = 0;
                mode = 1;

            }

        }



        motorlist.SetCommands(zero_vector, zero_vector, zero_vector, 1 * P_control_vector, 1 * D_control_vector, direction_vector);

        wait_rest_of_period(&P_info);
        current_time += 0.002;
        auto record_now_time = std::chrono::steady_clock::now();
        
        std::chrono::duration<double> elapsed_record = record_now_time - run_start_time;
        std::cout<<"elapsed_record"<<elapsed_record.count()<<std::endl;
        
        // std::cout<<"pos_actual: "<<pos_actual<<std::endl;
    }
    // std::this_thread::sleep_for(std::chrono::milliseconds(10000));



    return 0;
}


