#ifndef MAINEDITION_H
#define MAINEDITION_H
#include "./Udp/DataProcess.h"
#include "Types.h"
#include <list>
#include "MotorDrive.h"
#include <iostream>


using namespace std;



typedef struct
{
  uint16_t canId;
  uint16_t canline;
  float pos;
  float vel;
  float tor;

}PlanningPos;
class MainEdition
{
public:
   /**
     * @brief 初始化类
     *
     * @param id  电机canId
     * @param canlineid  can线id
     */
    MainEdition(uint16_t id);
    ~MainEdition();
    /**
     * @brief 配置主控板网络通信参数及建立连接
     *
     * @param LocalIp 本地IP地址
     * @param LocalPort 本地端口号,默认15020
     * @param DestIp 设置目标IP地址，默认*************
     * @return true 配置连接成功 false 配置连接失败
     * @warning 广播报文，你必须用ID 0x00FF的Motor对象使用
     */

    bool ConfigureNetworkParams(string LocalIp, uint16_t LocalPort = 15020,uint16_t DestPort=15000, string DestIp = "*************");
    

     /**
     * @brief 创建列表电机对象
     *
     * @param motorParamList 电机对象<canid,canlineid>列表
     * @param dataProcess   主板通信类
     * @param MotorList     接收返回的Motor对象列表
     * 
     */

    void CreateMotorDriverList(std::list<MotorCan>& motorParamList,std::list<std::shared_ptr<MotorDriver>>& MotorList);


      /**
     * @brief 创建单电机对象
     *
     * @param canId         canId
     * @param canLineId     can线Id
     * @param motorDrive    返回单个Motor对象
     * 
     */

    void CreateMotorDriver(u_int16_t canId,u_int16_t canLineId,std::shared_ptr<MotorDriver>& motorDrive);

    /**
     * @brief 关闭socket任务
     *
     * 
     */
    void CloseSocket();


    
    /**
     * @brief 设置pos大包
     * @param Pos    位置
     * @param Vel    速度
     * @param Cur    力矩
     * 
     */

    void SetPose(float Pos, float Vel, float Cur);

    /**
     * @brief  组大包发送
     * @param Pos    位置
     * @param Vel    速度
     * @param Cur    力矩
     * 
     */

    void setPlanningPose();


    bool SetFastMode(uint32_t Mode,uint32_t RepRate);

private:
    std::shared_ptr<DataProcess> m_dataProcess ;
    uint16_t Id;
    TotalParams TotalParamsData;
    FastParams FastParamsData;
    std::list<MotorCan> M_motorParamList;
    std::list<PlanningPos> m_panningPos;
    std::list<std::shared_ptr<MotorDriver>> m_MotorList;
};


#endif 