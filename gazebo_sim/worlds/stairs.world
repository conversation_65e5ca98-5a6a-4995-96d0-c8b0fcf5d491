<?xml version="1.0" ?>
<sdf version="1.5">
    <world name="default">
    <!-- <physics type="dart">
        <max_step_size>0.001</max_step_size>
        <real_time_factor>1.0</real_time_factor>
        <real_time_update_rate>1000</real_time_update_rate>
        <gravity>0 0 -9.81</gravity>
    </physics> -->
    <!-- <physics type="ode">
        <max_step_size>0.001</max_step_size>
        <real_time_factor>1</real_time_factor>
        <real_time_update_rate>1000</real_time_update_rate>
        <gravity>0 0 -9.81</gravity>
            <ode>
                <solver>
                    <type>quick</type>  
                    <iters>500</iters> 
                    <sor>0.8</sor>
                </solver>  
                <constraints>
                    <cfm>1e-8</cfm>
                    <erp>0.8</erp>
                    <contact_max_correcting_vel>10.0</contact_max_correcting_vel>
                    <contact_surface_layer>0.0001</contact_surface_layer>
                </constraints>  
            </ode>
        </physics> -->


        <gravity>0 0 -9.8</gravity>
        <physics default="1" name="default_physics" type="simbody">
        <real_time_update_rate>1000.0</real_time_update_rate>
        <max_contacts>8</max_contacts>
        <simbody>
            <accuracy>0.001</accuracy>
            <min_step_size>0.0005</min_step_size>
            <contact>
            <dissipation>100.0</dissipation>
            <viscous_friction>1</viscous_friction>
            <override_impact_capture_velocity>0.001</override_impact_capture_velocity>
            <override_stiction_transition_velocity>0.001</override_stiction_transition_velocity>
            <stiffness>1000.0</stiffness>
            <plastic_coef_restitution>0.1</plastic_coef_restitution>
            <dynamic_friction>0.9</dynamic_friction>
            <static_friction>0.9</static_friction>
            <plastic_impact_velocity>50</plastic_impact_velocity>
            </contact>
            <max_transient_velocity>0.5</max_transient_velocity>
        </simbody>
        <max_step_size>0.001</max_step_size>
        <real_time_factor>1.0</real_time_factor>
        </physics>
        <scene>
        <sky>
            <clouds>
                <speed>12</speed>
            </clouds>
        </sky>
        </scene>
        <!-- A global light source -->
        <include>
            <uri>model://sun</uri>
        </include>
        <!-- A ground plane -->
        <include>
            <uri>model://ground_plane</uri>
        </include>
        <!-- environment blocks, obstacles or stairs -->
        <model name="static_environment">
        <static>true</static>
            <link name="static_box">
                <pose>-12 2 0.5 0 0 0</pose>
                <collision name="static_box_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>


            <link name="static_box1">
                <pose>-11 2 0.4 0 0 0</pose>
                <collision name="static_box1_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box1_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>

            <link name="static_box2">
                <pose>-10 2 0.3 0 0 0</pose>
                <collision name="static_box2_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box2_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>
            <link name="static_box3">
                <pose>-9 2 0.2 0 0 0</pose>
                <collision name="static_box3_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box3_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>

            <link name="static_box4">
                <pose>-12 -2 0.5 0 0 0</pose>
                <collision name="static_box4_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box4_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>


            <link name="static_box5">
                <pose>-11 -2 0.4 0 0 0</pose>
                <collision name="static_box5_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box5_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>

            <link name="static_box6">
                <pose>-10 -2 0.3 0 0 0</pose>
                <collision name="static_box6_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box6_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>


            <link name="static_box7">
                <pose>-9 -2 0.2 0 0 0</pose>
                <collision name="static_box7_collision">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                </collision>
                <visual name="static_box7_visual">
                    <geometry>
                        <box>
                            <size>0.5 0.5 0.5</size>
                        </box>
                    </geometry>
                    <material>
                        <ambient>0.2 0.2 0.2 1.0</ambient>
                        <diffuse>.421 0.225 0.0 1.0</diffuse>
                    </material>
                </visual>
            </link>

            <link name='Stairs'>
                <pose>3 0 0 0 0 0</pose>
                <scale>1 1 1</scale>
                <visual name='Stairs_Visual_0'>
                    <pose>-1.26 -0 0.075 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_0'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>-1.26 -0 0.075 0 -0 1.5708</pose>
                </collision>

                <visual name='Stairs_Visual_1'>
                    <pose>-0.98 -0 0.225 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_1'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>-0.98 -0 0.225 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_2'>
                    <pose>-0.7 -0 0.375 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_2'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>-0.7 -0 0.375 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_3'>
                    <pose>-0.42 -0 0.525 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_3'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>-0.42 -0 0.525 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_4'>
                    <pose>-0.14 -0 0.675 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_4'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>-0.14 -0 0.675 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_5'>
                    <pose>0.14 0 0.825 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_5'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>0.14 0 0.825 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_6'>
                    <pose>0.42 0 0.975 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_6'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>0.42 0 0.975 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_7'>
                    <pose>0.7 0 1.125 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_7'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>0.7 0 1.125 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_8'>
                    <pose>0.98 0 1.275 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_8'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>0.98 0 1.275 0 -0 1.5708</pose>
                </collision>
                <visual name='Stairs_Visual_9'>
                    <pose>1.26 0 1.425 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='Stairs_Collision_9'>
                    <geometry>
                    <box>
                        <size>10 0.28 0.15</size>
                    </box>
                    </geometry>
                    <pose>1.26 0 1.425 0 -0 1.5708</pose>
                </collision>


                <!-- <visual name='wall0'>
                    <pose>-11 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.5 0.3</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall0_Collision'>
                    <geometry>
                    <box>
                        <size>10 0.5 0.3</size>
                    </box>
                    </geometry>
                    <pose>-11 -2 0 0 -0 1.5708</pose>
                </collision> -->


                <visual name='wall1'>
                    <pose>-11.4 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.7 0.3</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall1_Collision'>
                    <geometry>
                    <box>
                        <size>10 0.7 0.3</size>
                    </box>
                    </geometry>
                    <pose>-11.4 -2 0 0 -0 1.5708</pose>
                </collision>
                <visual name='wall2'>
                    <pose>-10.7 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.7 0.6</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall2_Collision'>
                    <geometry>
                    <box>
                        <size>10 0.7 0.6</size>
                    </box>
                    </geometry>
                    <pose>-10.7 -2 0 0 -0 1.5708</pose>
                </collision>


                <visual name='wall3'>
                    <pose>-10 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.7 0.9</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall3_Collision'>
                    <geometry>
                    <box>
                        <size>10 0.7 0.9</size>
                    </box>
                    </geometry>
                    <pose>-10 -2 0 0 -0 1.5708</pose>
                </collision>


                <visual name='wall4'>
                    <pose>-8.7 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 1.5 1.2</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall4_Collision'>
                    <geometry>
                    <box>
                        <size>10 1.5 1.2</size>
                    </box>
                    </geometry>
                    <pose>-8.7 -2 0 0 -0 1.5708</pose>
                </collision>


                <visual name='gap1'>
                    <pose>-7 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 1 1.4</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='gap1_Collision'>
                    <geometry>
                    <box>
                        <size>10 1 1.4</size>
                    </box>
                    </geometry>
                    <pose>-7 -2 0 0 -0 1.5708</pose>
                </collision>


                <visual name='gap2'>
                    <pose>-5.8 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.7 1.2</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='gap2_Collision'>
                    <geometry>
                    <box>
                        <size>10 0.7 1.2</size>
                    </box>
                    </geometry>
                    <pose>-5.8 -2 0 0 -0 1.5708</pose>
                </collision>

                <visual name='wall5'>
                    <pose>-16 -2 0 0 -0 1.5708</pose>
                    <geometry>
                    <box>
                        <size>10 0.5 1.5</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall5_Collision'>
                    <geometry>
                    <box>
                        <size>10 0.5 1.5</size>
                    </box>
                    </geometry>
                    <pose>-16 -2 0 0 -0 1.5708</pose>
                </collision>


                <!-- <visual name='wall6'>
                    <pose>-15 -1 0 0 -0 0</pose>
                    <geometry>
                    <box>
                        <size>4 0.5 1.8</size>
                    </box>
                    </geometry>
                    <material>
                    <script>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                        <name>Gazebo/Grey</name>
                    </script>
                    <ambient>1 1 1 1</ambient>
                    </material>
                    <meta>
                    <layer>0</layer>
                    </meta>
                </visual>
                <collision name='wall6_Collision'>
                    <geometry>
                    <box>
                        <size>4 0.5 1.8</size>
                    </box>
                    </geometry>
                    <pose>-15 -1 0 0 -0 0</pose>
                </collision> -->



            </link>
        </model>

    </world>
</sdf>
