<launch>


  <arg name="use_sim_time" default="true"/>



  <!-- <node pkg="tf" type="static_transform_publisher" name="world_to_robot" args="0.0 0.0 0.9 0 -3.14 0 /map /odom 200"/> -->








	<node pkg="point_lio" type="pointlio_mapping" name="laserMapping" output="screen" > 
    <rosparam command="load" file="$(find point_lio)/config/avia.yaml" />
    <param name="use_imu_as_input" type="bool" value="0"/> <!--change to 1 to use IMU as input of Point-LIO-->
    <param name="prop_at_freq_of_imu" type="bool" value="1"/>
    <param name="check_satu" type="bool" value="1"/>
    <param name="init_map_size" type="int" value="10"/>
    <param name="point_filter_num" type="int" value="1"/> <!--4, 3--> 
    <param name="space_down_sample" type="bool" value="0" />  
    <param name="filter_size_surf" type="double" value="0.05" /> <!--0.5, 0.3, 0.2, 0.15, 0.1--> 
    <param name="filter_size_map" type="double" value="0.05" /> <!--0.5, 0.3, 0.15, 0.1-->
    <param name="ivox_nearby_type" type="int" value="6" /> <!--0.5, 0.3, 0.15, 0.1-->
    <param name="runtime_pos_log_enable" type="bool" value="0" /> <!--1-->
	</node>



  <node pkg="elevation_mapping_demos" type="tf_to_pose_publisher.py" name="phybot_pose_publisher">
    <param name="from_frame" type="string" value="odom"/>
    <param name="to_frame" type="string" value="base_link"/>
  </node>

  <node pkg="elevation_mapping_demos" type="pc_filter.py" name="pc_filter" />

  <node pkg="elevation_mapping_demos" type="publish_map_frame.py" name="publish_map_frame" />

    <!-- <node pkg="elevation_mapping_demos" type="pc_filter.py" name="pc_filter" />
    <node pkg="elevation_mapping_demos" type="publish_map_frame.py" name="publish_map_frame" /> -->
  <!-- Elevation mapping node -->


  <node pkg="elevation_mapping" type="elevation_mapping" name="elevation_mapping" output="screen">
    <rosparam command="load" file="$(find elevation_mapping_demos)/config/robots/phybot_robot.yaml" />
    <rosparam command="load" file="$(find elevation_mapping)/config/sensor_processors/realsense_d435.yaml" />
    <rosparam command="load" file="$(find elevation_mapping_demos)/config/postprocessing/postprocessor_pipeline.yaml" />
  </node>

  <include file="$(find elevation_mapping_demos)/launch/visualization.launch" />






</launch>
