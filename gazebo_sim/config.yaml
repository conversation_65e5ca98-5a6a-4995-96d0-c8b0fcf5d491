gr1t1:
  stand_model_name: "stand_model_jit.pt"
  walk_model_name: "walk_model_jit.pt"
  dt: 0.001
  decimation: 20
  num_observations: 39
  clip_obs: 100.0
  clip_actions_lower: [-0.4391, -1.0491, -2.0991, -0.4391, -1.3991,
                       -1.1391, -1.0491, -2.0991, -0.4391, -1.3991]
  clip_actions_upper: [1.1391, 1.0491, 1.0491, 2.2691, 0.8691,
                       0.4391, 1.0491, 1.0491, 2.2691, 0.8691]
  rl_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
          57.0, 43.0, 114.0, 114.0, 15.3]
  rl_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
          5.7, 4.3, 11.4, 11.4, 1.5]
  fixed_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
             57.0, 43.0, 114.0, 114.0, 15.3]
  fixed_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
             5.7, 4.3, 11.4, 11.4, 1.5]
  num_of_dofs: 10
  action_scale: 1.0
  lin_vel_scale: 1.0
  ang_vel_scale: 1.0
  dof_pos_scale: 1.0
  dof_vel_scale: 1.0
  commands_scale: [1.0, 1.0, 1.0]
  torque_limits: [60.0, 45.0, 130.0, 130.0, 16.0,
                  60.0, 45.0, 130.0, 130.0, 16.0]
  default_dof_pos: [0.0, 0.0, -0.2618, 0.5236, -0.2618, 
                    0.0, 0.0, -0.2618, 0.5236, -0.2618]
  joint_controller_names: ["l_hip_roll_controller", "l_hip_yaw_controller", "l_hip_pitch_controller", "l_knee_pitch_controller", "l_ankle_pitch_controller", 
                           "r_hip_roll_controller", "r_hip_yaw_controller", "r_hip_pitch_controller", "r_knee_pitch_controller", "r_ankle_pitch_controller"]

gr1t2:
  stand_model_name: "stand_model_jit.pt"
  walk_model_name: "walk_model_jit.pt"
  dt: 0.001
  decimation: 20
  num_observations: 39
  clip_obs: 100.0
  clip_actions_lower: [-0.4391, -1.0491, -2.0991, -0.4391, -1.3991,
                       -1.1391, -1.0491, -2.0991, -0.4391, -1.3991]
  clip_actions_upper: [1.1391, 1.0491, 1.0491, 2.2691, 0.8691,
                       0.4391, 1.0491, 1.0491, 2.2691, 0.8691]
  rl_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
          57.0, 43.0, 114.0, 114.0, 15.3]
  rl_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
          5.7, 4.3, 11.4, 11.4, 1.5]
  fixed_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
             57.0, 43.0, 114.0, 114.0, 15.3]
  fixed_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
             5.7, 4.3, 11.4, 11.4, 1.5]
  num_of_dofs: 10
  action_scale: 1.0
  lin_vel_scale: 1.0
  ang_vel_scale: 1.0
  dof_pos_scale: 1.0
  dof_vel_scale: 1.0
  commands_scale: [1.0, 1.0, 1.0]
  torque_limits: [60.0, 45.0, 130.0, 130.0, 16.0,
                  60.0, 45.0, 130.0, 130.0, 16.0]
  default_dof_pos: [0.0, 0.0, -0.2618, 0.5236, -0.2618, 
                    0.0, 0.0, -0.2618, 0.5236, -0.2618]
  joint_controller_names: ["l_hip_roll_controller", "l_hip_yaw_controller", "l_hip_pitch_controller", "l_knee_pitch_controller", "l_ankle_pitch_controller", 
                           "r_hip_roll_controller", "r_hip_yaw_controller", "r_hip_pitch_controller", "r_knee_pitch_controller", "r_ankle_pitch_controller"]



phybot:
  stand_model_name: "policy_1.pt"
  walk_model_name: "policy_1.pt"
  dt: 0.002
  decimation: 10
  num_observations: 39
  clip_obs: 100.0
  clip_actions_lower: -100
  clip_actions_upper: 100.0
#   rl_kp: [300.0, 300.0, 300.0, 300.0, 100.0, 100.0,
#              300.0, 300.0, 300.0, 300.0, 100.0, 100.0,
#              300.0,300.0,
#              300.0, 300.0, 100.0, 
#              300.0, 300.0, 100.0]

  rl_kd: [2.0, 2.0, 2.0, 2.0, 2.0,2.0,
             2.0, 2.0, 2.0, 2.0, 2.0,2.0,
             2.0,2.0,
             2.0,2.0,1.0,
             2.0,2.0,1.0]

  rl_kp: [300.0, 300.0, 300.0, 300.0, 40.0, 40.0,
             300.0, 300.0, 300.0, 300.0, 40.0, 40.0,
             300.0,300.0,
             200.0, 200.0, 200.0, 
             200.0, 200.0, 200.0]
#   rl_kp: [200.0, 200.0, 200.0, 200.0, 40.0, 40.0,
#              200.0, 200.0, 200.0, 200.0, 40.0, 40.0,
#              300.0,300.0,
#              200.0, 200.0, 100.0, 
#              200.0, 200.0, 100.0]

#   rl_kd: [10.0, 10.0, 10.0, 10.0, 2.0,2.0,
#              10.0, 10.0, 10.0, 10.0, 2.0,2.0,
#              10.0,10.0,
#              10.0,10.0,2.0,
#              10.0,10.0,2.0]

  fixed_kp: [200.0, 200.0, 200.0, 200.0, 40.0, 40.0,
             200.0, 200.0, 200.0, 200.0, 40.0, 40.0,
             200.0,200.0,
             200.0, 200.0, 100.0, 
             200.0, 200.0, 100.0]
  fixed_kd: [2.0, 2.0, 2.0, 2.0, 2.0,2.0,
             2.0, 2.0, 2.0, 2.0, 2.0,2.0,
             2.0,2.0,
             2.0,2.0,1.0,
             2.0,2.0,1.0]
  num_of_dofs: 20
  history_len: 50
  num_of_heights: 625
  action_scale: 0.25
  lin_vel_scale: 2.0
  ang_vel_scale: 0.25
  dof_pos_scale: 1.0
  dof_vel_scale: 0.05
  commands_scale: [1.0, 1.0, 1.0, 1.0]
  torque_limits: [500.0, 200.0, 200.0, 500.0, 200.0,60.0,
                  500.0, 200.0, 200.0, 500.0, 200.0,60.0,
                  230.0,200.0,
                  200.0, 200.0, 58.0,
                  200.0, 200.0, 58.0]

  default_dof_pos: [-0.2, 0.0, 0.0, 0.4, -0.2, 0,
                    -0.2, 0.0, 0.0, 0.4, -0.2, 0,
                    0,0,
                    0,0.2,-1,
                    0,-0.2,-1]
  joint_controller_names: ["left_hip_pitch_controller", "left_hip_roll_controller", "left_hip_yaw_controller", "left_knee_controller", "left_ankle_pitch_controller", "left_ankle_roll_controller", 
                           "right_hip_pitch_controller", "right_hip_roll_controller", "right_hip_yaw_controller",  "right_knee_controller", "right_ankle_pitch_controller", "right_ankle_roll_controller", 
                            "waist_yaw_controller", "waist_roll_controller",
                            "left_shoulder_pitch_controller", "left_shoulder_roll_controller", "left_elbow_pitch_controller", 
                            "right_shoulder_pitch_controller", "right_shoulder_roll_controller", "right_elbow_pitch_controller"]
#   joint_controller_names: ["left_hip_pitch_controller/command", left_hip_roll_controller/command", "left_hip_yaw_controller/command", "left_knee_controller/command", "left_ankle_pitch_controller/command", "left_ankle_roll_controller/command", 
#                            "right_hip_pitch_controller/command", "right_hip_roll_controller/command", "right_hip_yaw_controller/command",  "right_knee_controller/command", "right_ankle_pitch_controller/command", "right_ankle_roll_controller/command", 
#                             "waist_yaw_controller/command", "waist_roll_controller/command",
#                             "left_shoulder_pitch_controller/command", "left_shoulder_roll_controller/command", "left_elbow_pitch_controller/command", 
#                             "right_shoulder_pitch_controller/command", "right_shoulder_roll_controller/command", "right_elbow_pitch_controller/command"]