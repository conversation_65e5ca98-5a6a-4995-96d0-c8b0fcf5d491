# RL Deploy Logs

This directory contains log files generated by the RL deployment system.

## Task Observation Logs

- **task_obs_log.csv**: Contains logged task observation values with the following columns:
  - `step_num`: Current step number
  - `frame_count`: Current frame count (for control_mode == 0)
  - `control_mode`: Current control mode (0 = motion library, 1 = manual control)
  - `task_obs_0` to `task_obs_254`: Individual task observation values (255 total)

## Configuration

Logging can be controlled via `RL_deploy/config/rl_params.yaml`:
- `enable_task_obs_logging`: Set to `true` to enable logging, `false` to disable
- `task_obs_log_path`: Path to the log file (relative to project root)

## Usage

The logging system automatically:
1. Creates the log file when the rl_deploy object is constructed
2. Writes a CSV header with column names
3. Logs task_obs values every time the Step() function is called (when decimation condition is met)
4. Flushes data to disk every 100 steps
5. Closes the log file when the rl_deploy object is destroyed

## File Format

CSV format with comma-separated values. Each row represents one logging event.
Values are logged with 6 decimal places precision.
