#ifndef RLDEPLOY_H
#define RLDEPLOY_H


#include "DataPackage/include/DataPackage.h"
#include <torch/script.h>
#include <fstream>
#include <iomanip>

class rl_deploy{

public:

    rl_deploy();
    ~rl_deploy();

    // void Init();

    void GetDataFromPackage(DataPackage &data);
    void SetDataToPackage(DataPackage &data);
    void Step();
    Eigen::MatrixXd LoadMotionLib(const std::string& filename, bool skipHeader);




private:
    torch::jit::script::Module policy;
    torch::Tensor torque_limits;

    torch::Tensor commands_scale;
    torch::Tensor default_dof_pos;
    torch::Tensor output_dof_pos;


    torch::Tensor lin_vel;           
    torch::Tensor last_lin_vel;           
    torch::Tensor ang_vel;      
    torch::Tensor gravity_vec;      
    torch::Tensor commands;
    torch::Tensor task_commands;    
    torch::Tensor base_euler;   
    torch::Tensor base_quat;   
    torch::Tensor dof_pos;           
    torch::Tensor dof_vel;           
    torch::Tensor actions;
    torch::Tensor task_obs;

    // torch::Tensor history_pos_buf;           
    // torch::Tensor history_vel_buf;           
    // torch::Tensor history_actions_buf;
    torch::Tensor history_proprio_obs_buf;

    int decimation;
    int num_observations;
    int num_task_obs;
    double clip_obs;
    double clip_actions_lower;
    double clip_actions_upper;
    double action_scale;
    // clip_actions_upper = torch::tensor(ReadVectorFromYaml<double>(config["clip_actions_upper"])).view({1, -1});
    // clip_actions_lower = torch::tensor(ReadVectorFromYaml<double>(config["clip_actions_lower"])).view({1, -1});
    int num_of_dofs;
    int history_len;
    double lin_vel_scale;
    double ang_vel_scale;
    double dof_pos_scale;
    double dof_vel_scale;
    double js_vx_desire;
    double js_vy_desire;
    double js_OmegaZ_control;
    int control_mode;

    int step_num = 0;
    int frame_count = 0;
    int num_motion_frames;
    std::vector<double> q;
    std::vector<double> dot_q;
    std::vector<double> imu_euler_zyx;
    std::vector<double> imu_quat;
    std::vector<double> imu_ang_vel;
    // Eigen::VectorXd q;
    // Eigen::VectorXd dot_q;
    std::vector<double> task_obs_vec;

    std::string policy_path;
    std::string motion_lib_path;
    std::vector<int> indices_to_remove;

    Eigen::VectorXd rl_p;
    Eigen::VectorXd rl_d;

    template<typename T>
    std::vector<T> ReadVectorFromYaml(const YAML::Node& node)
    {
        std::vector<T> values;
        for(const auto& val : node)
        {
            values.push_back(val.as<T>());
        }
        return values;
    }

    Eigen::MatrixXd motion_frames;

    torch::Tensor EulerZYXRotateInverse(torch::Tensor euler, torch::Tensor v);
    torch::Tensor QuatRotateInverse(torch::Tensor q, torch::Tensor v);
    Eigen::VectorXd remove_indices(const Eigen::VectorXd& input, const std::vector<int>& indices_to_remove);
    void assign_with_skipped_zero(Eigen::VectorXd& target,const Eigen::VectorXd& source,const std::vector<int>& indices_to_skip);


    // Logging members
    std::ofstream task_obs_log_file;
    bool enable_task_obs_logging;
    // Logging functions
    void InitTaskObsLogging(const std::string& log_file_path = "../RL_deploy/logs/task_obs_log.csv");
    void LogTaskObs(const torch::Tensor& task_obs_tensor);
    void CloseTaskObsLogging();
};



#endif // MODELPREDICTIVECONTROL_H