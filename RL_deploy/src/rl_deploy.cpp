#include "RL_deploy/include/rl_deploy.h"


// rl_deploy::rl_deploy()
// {

// }


rl_deploy::rl_deploy()
{
    torch::autograd::GradMode::set_enabled(false);
    std::string file_path = "../RL_deploy/config/rl_params.yaml";
    YAML::Node config = YAML::LoadFile(file_path);
    // load the policy
    policy_path = config["policy_path"].as<std::string>();
    policy = torch::jit::load(policy_path, torch::kCPU);
    std::cout << "policy init complete" << std::endl;
    // read offline motion library
    motion_lib_path = config["motion_lib_path"].as<std::string>();
    motion_frames = LoadMotionLib(motion_lib_path, false);

    // read rl params from yaml
    decimation = config["decimation"].as<int>();
    num_proprio = config["num_proprio"].as<int>();
    num_observations = config["num_observations"].as<int>();
    num_task_obs = config["num_task_obs"].as<int>();
    clip_obs = config["clip_obs"].as<double>();
    clip_actions_lower = config["clip_actions_lower"].as<double>();
    clip_actions_upper = config["clip_actions_upper"].as<double>();
    action_scale = config["action_scale"].as<double>();
    // clip_actions_upper = torch::tensor(ReadVectorFromYaml<double>(config["clip_actions_upper"])).view({1, -1});
    // clip_actions_lower = torch::tensor(ReadVectorFromYaml<double>(config["clip_actions_lower"])).view({1, -1});
    num_of_dofs = config["num_of_dofs"].as<int>();
    history_len = config["history_len"].as<int>();
    lin_vel_scale = config["lin_vel_scale"].as<double>();
    ang_vel_scale = config["ang_vel_scale"].as<double>();
    dof_pos_scale = config["dof_pos_scale"].as<double>();
    dof_vel_scale = config["dof_vel_scale"].as<double>();

    last_actions_eigen = Eigen::VectorXd::Zero(num_of_dofs);
    inputdata_eigen = Eigen::VectorXd::Zero(num_observations);
    outputdata_eigen = Eigen::VectorXd::Zero(num_of_dofs);
    hist_obs_buf_eigen = Eigen::VectorXd::Zero(history_len * num_proprio);

    default_dof_pos_eigen = ReadVectorFromYaml<double>(config["default_dof_pos"]);

    // history_pos_buf = torch::zeros({1, history_len * num_of_dofs});
    // history_vel_buf = torch::zeros({1, history_len * num_of_dofs});
    // history_actions_buf = torch::zeros({1, history_len * num_of_dofs});
    
    commands_scale = torch::tensor({lin_vel_scale, lin_vel_scale, ang_vel_scale});
    q.resize(num_of_dofs);
    dot_q.resize(num_of_dofs);
    imu_ang_vel.resize(3);
    imu_euler_zyx.resize(3);
    imu_quat.resize(4);

    torque_limits = torch::tensor(ReadVectorFromYaml<double>(config["torque_limits"])).view({1, -1});
    default_dof_pos = torch::tensor(ReadVectorFromYaml<double>(config["default_dof_pos"])).view({1, -1});
    indices_to_remove = ReadVectorFromYaml<int>(config["useless_joint_id"]);


    lin_vel = torch::tensor({{0.0, 0.0, 0.0}});
    last_lin_vel = torch::tensor({{0.0, 0.0, 0.0}});
    ang_vel = torch::tensor({{0.0, 0.0, 0.0}});
    // gravity_vec = torch::tensor({{0.0, 0.0, -1.0}});
    task_commands = torch::tensor({{0.0, 0.0, 0.0, 0.0}});  // the default height for root mode.
    commands = torch::tensor({{0.0, 0.0, 0.0}});  // the default height for root mode.
    base_euler = torch::tensor({{0.0, 0.0, 0.0}});
    dof_pos = default_dof_pos;
    dof_vel = torch::zeros({1, num_of_dofs});
    actions = torch::zeros({1, num_of_dofs});
    output_dof_pos = torch::zeros({1, num_of_dofs});


    
    // history_proprio_obs_buf = torch::zeros({1, history_len * num_observations});



    auto data = config["rl_p"];
    int N = data.size();  // 期望向量长度
    rl_p.resize(N);

    for (int i = 0; i < N; ++i) {
        rl_p(i) = data[i].as<double>();
    }

    data = config["rl_d"];
    N = data.size();  // 期望向量长度
    rl_d.resize(N);

    for (int i = 0; i < N; ++i) {
        rl_d(i) = data[i].as<double>();
    }

    // Initialize task_obs logging
    enable_task_obs_logging = config["enable_task_obs_logging"].as<bool>();
    if (enable_task_obs_logging) {
        std::string log_path = config["task_obs_log_path"].as<std::string>();
        InitTaskObsLogging(log_path);
    }

}

rl_deploy::~rl_deploy() {
    CloseTaskObsLogging();
}

void rl_deploy::GetDataFromPackage(DataPackage &DataPackage){

    js_vx_desire = DataPackage.js_vx_desire;


    imu_ang_vel.assign(DataPackage.imu_angular_vel.data(), DataPackage.imu_angular_vel.data() + DataPackage.imu_angular_vel.size());
    Eigen::Vector3d gravity_vec_eigen = Euler_ZYXToGravityVec(DataPackage.imu_zyx);
    imu_gravity_vec.assign(gravity_vec_eigen.data(), gravity_vec_eigen.data() + gravity_vec_eigen.size());

    imu_euler_zyx.assign(DataPackage.imu_zyx.data(), DataPackage.imu_zyx.data() + DataPackage.imu_zyx.size());   

    Eigen::Vector4d vec;
    vec << DataPackage.imu_quat.x(), DataPackage.imu_quat.y(), DataPackage.imu_quat.z(), DataPackage.imu_quat.w();  // 注意 Eigen 内部存储顺序是 x y z w

    imu_quat.assign(vec.data(), vec.data() + vec.size());   

    // std::cout<<"imu_euler_zyx: "<<imu_euler_zyx<<std::endl;
    Eigen::VectorXd q_origin = remove_indices(DataPackage.motor_pos, indices_to_remove);


    q.assign(q_origin.data(), q_origin.data() + q_origin.size());  


    Eigen::VectorXd dot_q_origin = remove_indices(DataPackage.motor_pos, indices_to_remove);

    dot_q.assign(dot_q_origin.data(), dot_q_origin.data() + dot_q_origin.size());    
    
    // dot_q = DataPackage.motor_vel.data();

    js_vx_desire = DataPackage.js_vx_desire;
    js_vy_desire = DataPackage.js_vy_desire;
    js_OmegaZ_control = DataPackage.js_OmegaZ_desire;
    control_mode = DataPackage.control_mode;
    // vx_Final = DataPackage.vx_Final;

}





void rl_deploy::SetDataToPackage(DataPackage &data)
{




    data.torq_desire.setZero() ;


    //******************************for real robot**************************************** */

    data.motor_Torque_desire.setZero();
    torch::Tensor output_dof_pos_double = output_dof_pos.to(torch::kDouble);

    // 用 Eigen::Map 映射到 VectorXd（注意：保持 tensor 是 contiguous）
    // output_dof_pos_double = output_dof_pos_double.contiguous();
    // std::cout<<"1:  "<<output_dof_pos_double<<std::endl;

    Eigen::VectorXd output_dof_pos_origin = Eigen::Map<Eigen::VectorXd>(
        output_dof_pos_double.data_ptr<double>(),
        output_dof_pos_double.numel()
    );


    // std::cout<<"output_dof_pos_origin: "<<output_dof_pos_origin<<std::endl;
    assign_with_skipped_zero(data.motor_Pos_desire, output_dof_pos_origin, indices_to_remove);

    // std::cout<<"2: "<<output_dof_pos_origin<<std::endl;
    // data.motor_Pos_desire.setZero() ;
    data.motor_Vel_desire.setZero() ;

    data.sim_P = rl_p*1.0;
    data.sim_D = rl_d*1.0;
}


Eigen::MatrixXd rl_deploy::LoadMotionLib(const std::string& filename, bool skipHeader) {
    std::ifstream file(filename);
    std::string line;
    std::vector<std::vector<double>> data;

    // read w/o process
    if (skipHeader) {
        std::getline(file, line);
    }

    while (std::getline(file, line)) {
        std::stringstream ss(line);
        std::string value;
        std::vector<double> row;

        while (std::getline(ss, value, ',')) {
            try {
                row.push_back(std::stod(value));
            } catch (...) {
                row.push_back(0.0);  // 可以自定义处理方式
            }
        }

        if (!row.empty())
            data.push_back(row);
    }

    if (data.empty()) {
        throw std::runtime_error("No data found in file: " + filename);
    }

    int rows = data.size();
    int cols = data[0].size();
    num_motion_frames = rows;
    Eigen::MatrixXd mat(rows, cols);

    for (int i = 0; i < rows; ++i)
        mat.row(i) = Eigen::VectorXd::Map(&data[i][0], cols);
    std::cout << "motion lib loaded !!!" << std::endl;

    return mat;
}


void rl_deploy::Step()
{
    step_num += 1;


    if(step_num % decimation == 0)
    {   
        ang_vel = torch::tensor(imu_ang_vel).unsqueeze(0);
        // std::cout<<"commands: "<<commands<<std::endl;
        base_euler = torch::tensor(imu_euler_zyx).unsqueeze(0);
        base_quat = torch::tensor(imu_quat).unsqueeze(0);        
        dof_pos = torch::tensor(q).narrow(0, 0, num_of_dofs).unsqueeze(0);
        dof_vel = torch::tensor(dot_q).narrow(0, 0, num_of_dofs).unsqueeze(0);
        gravity_vec = torch::tensor(imu_gravity_vec).unsqueeze(0);

        // std::cout<<"the current control mode is: "<< control_mode << std::endl;
        // commands = torch::tensor({{this->cmd_vel.linear.x, this->cmd_vel.linear.y, this->cmd_vel.angular.z}});
        if (control_mode == 1)
        {
            task_commands = torch::tensor({{js_vx_desire, 0.0, js_OmegaZ_control, 0.68}});
            commands = torch::tensor({{js_vx_desire, 0.0, js_OmegaZ_control}});
            task_obs = torch::zeros({1, num_task_obs});
        }
        else if (control_mode == 0)
        {
            frame_count += 1;
            int frame_id = (frame_count - 1) % num_motion_frames;  // Start from row 0
            auto segment = motion_frames.row(frame_id);

            // Verify we have enough columns in the motion file
            if (segment.size() < 4 + num_task_obs) {
                std::cerr << "Error: Motion file has " << segment.size() << " columns, but expected "
                         << (4 + num_task_obs) << " columns (4 task_commands + " << num_task_obs << " task_obs)" << std::endl;
                task_commands = torch::zeros({1, 4});
                task_obs = torch::zeros({1, num_task_obs});
            } else {
                // Extract task_commands (first 4 columns: 0, 1, 2, 3) - using explicit approach
                std::vector<double> task_commands_vec;
                task_commands_vec.reserve(4);
                for (int i = 0; i < 4; ++i) {
                    task_commands_vec.push_back(segment(i));
                }
                // Extract task_obs (columns 4 to 4+num_task_obs-1)
                std::vector<double> task_obs_vec;
                task_obs_vec.reserve(num_task_obs);
                for (int i = 4; i < 4 + num_task_obs; ++i) {
                    task_obs_vec.push_back(segment(i));
                }

                // Create tensors
                task_commands = torch::tensor(task_commands_vec, torch::kFloat64).view({1, 4}).to(torch::kFloat32);
                task_obs = torch::tensor(task_obs_vec, torch::kFloat64).view({1, num_task_obs}).to(torch::kFloat32);
            }
        }

//******************************phase******************************
        float period = 0.8f;
        float offset = 0.5f;

        // 将 period 转成与 episode_length_buf 相同设备的 Tensor（避免 CPU/GPU mismatch）
        torch::Tensor episode_length_buf = torch::tensor({step_num}, torch::kFloat32);  // [5]

        torch::Tensor period_tensor = torch::full_like(episode_length_buf, period);  // [5]

        // 计算 phase
        torch::Tensor phase = torch::fmod(episode_length_buf * 0.002, period_tensor) / period_tensor;  // [5]

        // 继续构造其他 Tensor：
        torch::Tensor phase_left = phase;
        torch::Tensor phase_right = torch::fmod(phase + offset, 1.0f);

        // torch::Tensor leg_phase = torch::cat({phase_left.unsqueeze(1), phase_right.unsqueeze(1)}, /*dim=*/-1);


        const float PI = 3.14159265358979323846f;

        // 假设 phase 是上一步得到的 Tensor
        torch::Tensor sin_phase = torch::sin(2.0f * PI * phase).unsqueeze(1);
        torch::Tensor cos_phase = torch::cos(2.0f * PI * phase).unsqueeze(1);

//******************************phase******************************

        // EulerZYXRotateInverse(base_euler, ang_vel) * ang_vel_scale,
        // std::cout<<"actions: "<<actions<<std::endl;
        torch::Tensor obs = torch::cat({
            task_commands,
            // commands,
            // this->QuatRotateInverse(base_quat, lin_vel)  * lin_vel_scale,
            ang_vel * ang_vel_scale, // TODO is QuatRotateInverse necessery?
            // QuatRotateInverse(base_quat, ang_vel) * ang_vel_scale,
            // EulerZYXRotateInverse(base_euler, ang_vel) * ang_vel_scale,
            // EulerZYXRotateInverse(base_euler, gravity_vec),
            gravity_vec,
            // QuatRotateInverse(base_quat, gravity_vec),
            (dof_pos - default_dof_pos) * dof_pos_scale,
            dof_vel * dof_vel_scale,
            actions,
            task_obs,
            history_proprio_obs_buf,
            // tensor_height,
            // sin_phase,
            // cos_phase,

            }, 1);
        

        // std::cout<<"vel_history_buf_short * 0,: "<<obs<<std::endl;
        // torch::Tensor clamped_obs = torch::clamp(obs, -clip_obs, clip_obs);
        // clamped_obs.zero_();
        actions = policy.forward({obs}).toTensor();
        // std::cout<<"1: "<<actions<<std::endl;
        // this->TorqueProtect(origin_output_torques);
        output_dof_pos = actions * action_scale + default_dof_pos;

        torch::Tensor history_proprio_obs_trimmed = history_proprio_obs_buf.index({torch::indexing::Slice(), torch::indexing::Slice(num_observations, torch::indexing::None)});
        torch::Tensor current_proprio_obs = obs.index({torch::indexing::Slice(), torch::indexing::Slice(0, num_observations)});
        history_proprio_obs_buf = torch::cat({history_proprio_obs_trimmed, current_proprio_obs}, 1);

        // Log task_obs values
        if (enable_task_obs_logging) {
            LogTaskObs(current_proprio_obs);
        }

    }
    // std::cout<<"clamped_obs: "<<std::endl;
        // std::cout<<"commands: "<<commands<<std::endl;

}



torch::Tensor rl_deploy::QuatRotateInverse(torch::Tensor q, torch::Tensor v)
{
    c10::IntArrayRef shape = q.sizes();
    torch::Tensor q_w = q.index({torch::indexing::Slice(), -1});
    torch::Tensor q_vec = q.index({torch::indexing::Slice(), torch::indexing::Slice(0, 3)});
    torch::Tensor a = v * (2.0 * torch::pow(q_w, 2) - 1.0).unsqueeze(-1);
    torch::Tensor b = torch::cross(q_vec, v, -1) * q_w.unsqueeze(-1) * 2.0;
    torch::Tensor c = q_vec * torch::bmm(q_vec.view({shape[0], 1, 3}), v.view({shape[0], 3, 1})).squeeze(-1) * 2.0;
    return a - b + c;
}


torch::Tensor rl_deploy::EulerZYXRotateInverse(torch::Tensor euler, torch::Tensor v)
{
    // 输入: euler: [N, 3], 每行为 [yaw, pitch, roll]，即 ZYX 顺序
    //      v: [N, 3]，表示待旋转向量
    // 输出: 经过逆旋转（即转到局部坐标）后的向量

    auto yaw = euler.index({torch::indexing::Slice(), 0}) * 0;
    auto pitch = euler.index({torch::indexing::Slice(), 1});
    auto roll = euler.index({torch::indexing::Slice(), 2});

    auto cy = torch::cos(yaw);
    auto sy = torch::sin(yaw);
    auto cp = torch::cos(pitch);
    auto sp = torch::sin(pitch);
    auto cr = torch::cos(roll);
    auto sr = torch::sin(roll);

    // 构造旋转矩阵 R（ZYX）
    // R = Rz(yaw) * Ry(pitch) * Rx(roll)
    // 最终 shape: [N, 3, 3]
    torch::Tensor R = torch::zeros({euler.size(0), 3, 3}, euler.options());

    R.index_put_({torch::indexing::Slice(), 0, 0}, cy * cp);
    R.index_put_({torch::indexing::Slice(), 0, 1}, cy * sp * sr - sy * cr);
    R.index_put_({torch::indexing::Slice(), 0, 2}, cy * sp * cr + sy * sr);

    R.index_put_({torch::indexing::Slice(), 1, 0}, sy * cp);
    R.index_put_({torch::indexing::Slice(), 1, 1}, sy * sp * sr + cy * cr);
    R.index_put_({torch::indexing::Slice(), 1, 2}, sy * sp * cr - cy * sr);

    R.index_put_({torch::indexing::Slice(), 2, 0}, -sp);
    R.index_put_({torch::indexing::Slice(), 2, 1}, cp * sr);
    R.index_put_({torch::indexing::Slice(), 2, 2}, cp * cr);

    // v: [N, 3] -> [N, 3, 1]
    auto v_ = v.view({v.size(0), 3, 1});

    // 逆旋转 = R^T @ v
    auto R_T = R.transpose(1, 2);  // [N, 3, 3]
    auto v_rotated = torch::bmm(R_T, v_).squeeze(-1);  // [N, 3]

    return v_rotated;
}


Eigen::VectorXd rl_deploy::remove_indices(const Eigen::VectorXd& input, const std::vector<int>& indices_to_remove) 
{
    int original_size = input.size();

    // 构建保留的索引列表
    std::vector<int> keep_indices;
    for (int i = 0; i < original_size; ++i) {
        if (std::find(indices_to_remove.begin(), indices_to_remove.end(), i) == indices_to_remove.end()) {
            keep_indices.push_back(i);
        }
    }

    // 构造新向量
    Eigen::VectorXd output(keep_indices.size());
    for (size_t i = 0; i < keep_indices.size(); ++i) {
        output[i] = input[keep_indices[i]];
    }

    return output;
}


Eigen::Vector3d rl_deploy::Euler_ZYXToGravityVec(Eigen::Vector3d euler_a) {
    Eigen::Matrix3d R = Eigen::Matrix3d::Identity();
    R = Eigen::AngleAxisd(euler_a[0], Eigen::Vector3d::UnitZ()).toRotationMatrix() *
        Eigen::AngleAxisd(euler_a[1], Eigen::Vector3d::UnitY()).toRotationMatrix() *
        Eigen::AngleAxisd(euler_a[2], Eigen::Vector3d::UnitX()).toRotationMatrix();
    Eigen::Vector3d grav_vec = -R.transpose().col(2);
    return grav_vec;
}


void rl_deploy::assign_with_skipped_zero(
    Eigen::VectorXd& target,
    const Eigen::VectorXd& source,
    const std::vector<int>& indices_to_skip)
{
    int target_size = target.size();
    int source_size = source.size();

    std::unordered_set<int> skip_set(indices_to_skip.begin(), indices_to_skip.end());

    int source_idx = 0;

    for (int i = 0; i < target_size; ++i) {
        if (skip_set.count(i)) {
            target[i] = 0.0;  // 跳过位置赋0
        } else {
            if (source_idx < source_size) {
                target[i] = source[source_idx++];
            } else {
                target[i] = 0.0;  // 如果 source 不够长，剩余位置补0（可选）
            }
        }
    }

    // 可选：保证 source 完全用完
    assert(source_idx == source_size && "Source vector is longer than available non-skipped slots");
}


// Task observation logging functions
void rl_deploy::InitTaskObsLogging(const std::string& log_file_path) {
    try {
        // Create logs directory if it doesn't exist
        std::string dir_path = "../RL_deploy/logs/";
        system(("mkdir -p " + dir_path).c_str());

        task_obs_log_file.open(log_file_path, std::ios::out | std::ios::trunc);
        if (!task_obs_log_file.is_open()) {
            std::cerr << "Failed to open task_obs log file: " << log_file_path << std::endl;
            enable_task_obs_logging = false;
            return;
        }

        // Write CSV header
        task_obs_log_file << "step_num,frame_count,control_mode";
        for (int i = 0; i < num_observations; ++i) {
            task_obs_log_file << ",proprio_obs_" << i;
        }
        task_obs_log_file << std::endl;

        std::cout << "Task observation logging initialized: " << log_file_path << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error initializing task_obs logging: " << e.what() << std::endl;
        enable_task_obs_logging = false;
    }
}

void rl_deploy::LogTaskObs(const torch::Tensor& task_obs_tensor) {
    if (!enable_task_obs_logging || !task_obs_log_file.is_open()) {
        return;
    }

    try {
        // Convert tensor to CPU and double precision for logging
        torch::Tensor task_obs_cpu = task_obs_tensor.to(torch::kCPU).to(torch::kDouble);

        // Write step info
        task_obs_log_file << step_num << "," << frame_count << "," << control_mode;

        // Write task_obs values
        auto accessor = task_obs_cpu.accessor<double, 2>();
        for (int i = 0; i < num_observations; ++i) {
            task_obs_log_file << " " << std::fixed << std::setprecision(6) << accessor[0][i];
        }
        task_obs_log_file << std::endl;

        // Flush every 100 steps to ensure data is written
        if (step_num % 100 == 0) {
            task_obs_log_file.flush();
        }
    } catch (const std::exception& e) {
        std::cerr << "Error logging task_obs: " << e.what() << std::endl;
    }
}

void rl_deploy::CloseTaskObsLogging() {
    if (task_obs_log_file.is_open()) {
        task_obs_log_file.close();
        std::cout << "Task observation logging closed." << std::endl;
    }
}


